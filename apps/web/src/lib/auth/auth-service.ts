import { supabase } from "@/lib/supabase";
import { Session, User } from "@supabase/supabase-js";
import { clearOrganizationCache } from "./organization-cache-exports";

/**
 * Get the current auth session
 */
export async function getCurrentSession(): Promise<Session | null> {
  try {
    const { data } = await supabase.auth.getSession();
    return data.session;
  } catch (error) {
    console.error("Error getting current session:", error);
    return null;
  }
}

/**
 * Sign in with email and password
 */
export async function signInWithPassword(
  email: string,
  password: string,
): Promise<{ user: User | null; error: Error | null }> {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return { user: null, error };
    }

    return { user: data.user, error: null };
  } catch (error) {
    return { user: null, error: error as Error };
  }
}

/**
 * Sign up with email and password
 */
export async function signUpWithPassword(
  email: string,
  password: string,
  userData?: { [key: string]: any },
): Promise<{ user: User | null; error: Error | null }> {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData,
      },
    });

    if (error) {
      return { user: null, error };
    }

    return { user: data.user, error: null };
  } catch (error) {
    return { user: null, error: error as Error };
  }
}

/**
 * Sign out the current user
 * Uses a robust approach that works around Supabase local dev logout issues
 */
export async function signOut(): Promise<void> {
  try {
    console.log("[AUTH_SERVICE] Starting sign out process");

    // For local development, bypass the problematic server logout
    // and just clear everything locally
    if (import.meta.env.VITE_SUPABASE_URL?.includes('127.0.0.1') ||
        import.meta.env.VITE_SUPABASE_URL?.includes('localhost')) {
      console.log("[AUTH_SERVICE] Local development detected, using manual logout");

      // Just clear all auth-related storage without calling the server
      try {
        // Clear Supabase auth storage keys
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('sb-') || key.includes('supabase') || key.includes('auth')) {
            console.debug(`[AUTH_SERVICE] Removing localStorage key: ${key}`);
            localStorage.removeItem(key);
          }
        });

        const sessionKeys = Object.keys(sessionStorage);
        sessionKeys.forEach(key => {
          if (key.startsWith('sb-') || key.includes('supabase') || key.includes('auth')) {
            console.debug(`[AUTH_SERVICE] Removing sessionStorage key: ${key}`);
            sessionStorage.removeItem(key);
          }
        });

        console.log("[AUTH_SERVICE] Local storage cleared successfully");
      } catch (storageError) {
        console.warn("[AUTH_SERVICE] Error clearing storage:", storageError);
      }
    } else {
      // For production, use normal logout
      console.log("[AUTH_SERVICE] Production environment, using normal logout");
      const { error } = await supabase.auth.signOut({ scope: 'local' });

      if (error) {
        console.error("[AUTH_SERVICE] Supabase signOut error:", error);
        // Don't throw - we still want to clear local cache even if server logout fails
      } else {
        console.log("[AUTH_SERVICE] Supabase signOut successful");
      }
    }

    clearOrganizationCache();
    console.log("[AUTH_SERVICE] Sign out process completed");
  } catch (error) {
    console.error("[AUTH_SERVICE] Error during sign out:", error);
    // Still clear cache even if there's an error
    clearOrganizationCache();
  }
}

/**
 * Reset password
 */
export async function resetPassword(
  email: string,
): Promise<{ error: Error | null }> {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    return { error };
  } catch (error) {
    return { error: error as Error };
  }
}

/**
 * Update password
 */
export async function updatePassword(
  newPassword: string,
): Promise<{ error: Error | null }> {
  try {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    return { error };
  } catch (error) {
    return { error: error as Error };
  }
}
