import {
  authStateReducer,
  getOrganization,
  getUser,
} from "@/lib/auth/auth-state-machine";
import { clearOrganizationCache } from "@/lib/auth/organization-cache";
import { supabase } from "@/lib/supabase";
import { useOrganizationStore } from "@/stores/organization-store";
import type {
  AuthResponse,
  Session,
  UserResponse,
} from "@supabase/supabase-js";
import console from "console";
import { useCallback, useEffect, useMemo, useReducer, useRef, useState } from "react";
import { AuthContext } from "./auth-context";
import { AuthContextType, Organization } from "./auth-context-types";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Use the state machine to manage auth state
  const [authState, dispatch] = useReducer(authStateReducer, {
    status: "initializing",
  });

  // Track session state
  const [session, setSession] = useState<Session | null>(null);

  // Track if we've completed initial auth check
  const [hasCompletedInitialCheck, setHasCompletedInitialCheck] = useState(false);

  // Track if component is mounted to avoid state updates after unmount
  const isMounted = useRef(true);

  // Track if we've already loaded organization data for the current user
  const loadedOrgForUser = useRef<string | null>(null);

  // Use the organization store
  const { currentOrg, loadUserOrganizations, clearCache: clearOrganizationStore } = useOrganizationStore();

  // Auth methods
  const handleSignUp = useCallback(
    async (
      email: string,
      password: string,
      metadata?: {
        first_name?: string;
        last_name?: string;
        organization_id?: string;
      },
    ): Promise<AuthResponse> => {
      return supabase.auth.signUp({
        email,
        password,
        options: { data: metadata },
      });
    },
    [],
  );

  const handleSignIn = useCallback(
    async (email: string, password: string): Promise<AuthResponse> => {
      console.debug("[AUTH] Starting sign in process");
      const result = await supabase.auth.signInWithPassword({
        email: email,
        password: password,
      });

      if (result.data.session) {
        console.debug("[AUTH] Sign in successful, session established");
        // Manually update auth state as fallback in case the listener doesn't fire
        console.debug("[AUTH] Manually updating auth state after successful login");
        setSession(result.data.session);
        dispatch({ type: "SIGN_IN", user: result.data.session.user });

        // Set a flag in sessionStorage to show login success toast on dashboard
        sessionStorage.setItem('spritely_login_success', 'true');
      } else if (result.error) {
        console.error("[AUTH] Sign in failed:", result.error.message);
      }

      return result;
    },
    [],
  );

  const handleResetPassword = useCallback(
    async (
      email: string,
    ): Promise<{ data: object | null; error: Error | null }> => {
      try {
        const { data, error } =
          await supabase.auth.resetPasswordForEmail(email);
        return { data, error: error as Error | null };
      } catch (error) {
        return { data: null, error: error as Error };
      }
    },
    [],
  );

  const handleUpdatePassword = useCallback(
    async (newPassword: string): Promise<UserResponse> => {
      return supabase.auth.updateUser({ password: newPassword });
    },
    [],
  );

  const handleSignOut = useCallback(async () => {
    try {
      console.debug("[AUTH] Starting logout process");

      // Clear organization cache first
      clearOrganizationCache();

      // Clear organization store
      clearOrganizationStore();

      // Sign out from Supabase with scope 'global' to clear all sessions
      const { error } = await supabase.auth.signOut({ scope: 'global' });

      if (error) {
        console.error("[AUTH] Supabase signOut error:", error);
        // Don't throw - we still want to clear local state even if server logout fails
      } else {
        console.debug("[AUTH] Supabase sign out successful");
      }

      // Immediately clear local auth state regardless of Supabase response
      // This ensures logout works even if the auth state change listener doesn't fire
      console.debug("[AUTH] Clearing local auth state");
      dispatch({ type: "SIGN_OUT" });
      setSession(null);

      // Also manually clear Supabase storage
      try {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('sb-')) {
            localStorage.removeItem(key);
          }
        });
        console.debug("[AUTH] Cleared Supabase tokens from localStorage");
      } catch (storageError) {
        console.warn("[AUTH] Error clearing Supabase tokens:", storageError);
      }

      console.debug("[AUTH] Sign out completed successfully");
    } catch (error) {
      console.error("[AUTH] Error during sign out:", error);

      // Even if Supabase logout fails, ensure we clear local state
      console.debug("[AUTH] Forcing local logout due to error");
      dispatch({ type: "SIGN_OUT" });
      setSession(null);

      // Also manually clear any remaining auth tokens
      try {
        localStorage.removeItem('supabase.auth.token');
        sessionStorage.removeItem('supabase.auth.token');
      } catch (storageError) {
        console.warn("[AUTH] Error clearing auth tokens:", storageError);
      }
    }
  }, [clearOrganizationStore]);

  const handleSetOrganization = useCallback(
    (org: Organization | null) => {
      if (org) {
        console.info(`[AUTH] Setting organization: ${org.name} (${org.id})`);
        dispatch({ type: "LOAD_ORGANIZATION", organization: org });
      } else {
        console.info("[AUTH] Clearing organization (sign out)");
        dispatch({ type: "SIGN_OUT" });
      }
    },
    [],
  );

  // Load organization data when currentOrg changes from the store
  useEffect(() => {
    if (currentOrg && session?.user) {
      const currentOrgFromState = getOrganization(authState);
      if (!currentOrgFromState || currentOrgFromState.id !== currentOrg.id) {
        console.info(
          `[AUTH] Syncing organization from store: ${currentOrg.name} (${currentOrg.id})`,
        );
        dispatch({
          type: "LOAD_ORGANIZATION",
          organization: currentOrg,
        });

        // Ensure URL parameters are updated when auth context changes
        if (typeof window !== 'undefined') {
          const url = new URL(window.location.href);
          if (currentOrg.id === "system-admin-all-orgs") {
            url.searchParams.set("org", "all");
          } else {
            url.searchParams.set("org", currentOrg.id);
          }
          window.history.replaceState({}, '', url.toString());
        }
      }
    }
  }, [currentOrg, session?.user, authState]);

  // Load organization data when user signs in
  useEffect(() => {
    if (session?.user && !loadedOrgForUser.current) {
      const userId = session.user.id;
      console.info(`[AUTH] Loading organization data for user: ${userId}`);
      loadedOrgForUser.current = userId;

      loadUserOrganizations(session.user).catch((error) => {
        console.error("[AUTH] Error loading organization data:", error);
        loadedOrgForUser.current = null;
        if (isMounted.current) {
          dispatch({
            type: "ERROR",
            error: new Error("Failed to load organization data"),
          });
        }
      });
    }
  }, [session?.user, loadUserOrganizations]);



  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.debug("[AUTH] Initializing auth state...");
        // Get initial session
        const { data: { session: initialSession }, error } = await supabase.auth.getSession();

        console.debug("[AUTH] Initial session check", {
          hasSession: !!initialSession,
          hasUser: !!initialSession?.user,
          error: !!error
        });

        if (initialSession) {
          console.debug("[AUTH] Found initial session, signing in user");
          setSession(initialSession);
          dispatch({ type: "SIGN_IN", user: initialSession.user });
        } else {
          console.debug("[AUTH] No initial session found, setting unauthenticated");
          dispatch({ type: "SIGN_OUT" });
        }

        // Mark initial check as complete
        setHasCompletedInitialCheck(true);
      } catch (error) {
        console.error("[AUTH] Error initializing auth:", error);
        setHasCompletedInitialCheck(true);
        if (isMounted.current) {
          dispatch({ type: "ERROR", error: error as Error });
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!isMounted.current) return;

        console.debug(`[AUTH] Auth state changed: ${event}`, { session: !!session, user: !!session?.user });
        setSession(session);

        if (event === "SIGNED_IN" && session?.user) {
          console.debug("[AUTH] User signed in, updating state immediately");
          dispatch({ type: "SIGN_IN", user: session.user });
          // Reset the loaded flag so we load org data for the new user
          loadedOrgForUser.current = null;
        } else if (event === "SIGNED_OUT") {
          console.debug("[AUTH] User signed out, clearing state immediately");
          dispatch({ type: "SIGN_OUT" });
          clearOrganizationCache();
          clearOrganizationStore();
          loadedOrgForUser.current = null;
        } else if (event === "TOKEN_REFRESHED" && session?.user) {
          // Token refresh doesn't need to reload organization data
          console.debug("[AUTH] Token refreshed, updating user");
          dispatch({ type: "SIGN_IN", user: session.user });
        }
      },
    );

    return () => {
      isMounted.current = false;
      subscription.unsubscribe();
    };
  }, [clearOrganizationStore]);

  // Calculate final organization and loading state
  const finalOrganization = getOrganization(authState) || currentOrg;

  // Only show loading screen on true initial app loads, not page refreshes
  // Check if we have cached session data to detect page refresh vs fresh load
  const hasCachedSession = useMemo(() => {
    try {
      // Check for Supabase auth tokens in storage
      const localStorageKeys = Object.keys(localStorage);
      const sessionStorageKeys = Object.keys(sessionStorage);

      const hasSupabaseToken = localStorageKeys.some(key => key.startsWith('sb-')) ||
                              sessionStorageKeys.some(key => key.startsWith('sb-'));

      return hasSupabaseToken;
    } catch {
      return false;
    }
  }, []);

  // Don't show loading if we have cached session data (page refresh) or if auth check is complete
  const finalIsLoading = !hasCompletedInitialCheck && !hasCachedSession;

  // Create context value
  const userFromState = getUser(authState);
  console.debug("[AUTH_CONTEXT] Creating context value", {
    authState: authState.status,
    userFromState: !!userFromState,
    session: !!session,
    finalIsLoading
  });

  const contextValue: AuthContextType = {
    // State
    user: userFromState,
    session,
    organization: finalOrganization,
    isLoading: finalIsLoading,
    hasOrganization: !!finalOrganization,
    authState,

    // Actions
    signUp: handleSignUp,
    signIn: handleSignIn,
    signOut: handleSignOut,
    resetPassword: handleResetPassword,
    updatePassword: handleUpdatePassword,
    setOrganization: handleSetOrganization,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}
