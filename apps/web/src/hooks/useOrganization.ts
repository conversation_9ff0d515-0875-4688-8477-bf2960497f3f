import { useOrganizationStore } from "@/stores/organization-store";
import { useEffect } from "react";
import { useAuth } from "./useAuth";

export function useOrganization() {
  const { user } = useAuth();
  const {
    currentOrg,
    availableOrgs,
    isSystemAdmin,
    isLoading,
    error,
    loadUserOrganizations,
    switchOrganization,
    restoreFromUrl,
    clearCache
  } = useOrganizationStore();

  // Auto-load organizations when user changes
  useEffect(() => {
    if (user && availableOrgs.length === 0 && !isLoading) {
      loadUserOrganizations(user);
    }
  }, [user, availableOrgs.length, isLoading, loadUserOrganizations]);

  // Clear cache when user signs out
  useEffect(() => {
    if (!user) {
      clearCache();
    }
  }, [user, clearCache]);

  return {
    currentOrg,
    availableOrgs,
    isLoading,
    error,
    switchOrganization,
    restoreFromUrl,
    hasMultipleOrgs: availableOrgs.length > 1,
    isSystemAdmin
  };
}