import { useToast } from "@/components/ui/use-toast";
import { useEffect } from "react";

/**
 * Hook to show a login success toast if the user just logged in
 * This checks for a flag in sessionStorage and shows the toast once
 */
export function useLoginSuccessToast() {
  const { toast } = useToast();

  useEffect(() => {
    // Check if user just logged in
    const loginSuccess = sessionStorage.getItem('spritely_login_success');

    if (loginSuccess === 'true') {
      // Remove the flag immediately to prevent showing again
      sessionStorage.removeItem('spritely_login_success');

      // Show the success toast
      toast({
        variant: "success",
        title: "Welcome back!",
        description: "You have been successfully signed in.",
      });
    }
  }, [toast]);
}
