import { createCacheKey, requestDeduplicator } from "@/lib/request-deduplication";
import { supabase } from "@/lib/supabase";
import type { Database } from "@spritely/supabase-types";
import { User } from "@supabase/supabase-js";

type Organization = Database["public"]["Tables"]["organizations"]["Row"];

export async function getUserOrganizations(user: User): Promise<Organization[]> {
  return requestDeduplicator.deduplicate(
    createCacheKey.organizations(user.id),
    async () => {
      const userRoles = await getUserRoles(user.id);
      console.log("[ORG_SERVICE] User roles for", user.id, ":", userRoles);

      if (!userRoles?.length) {
        console.log("[ORG_SERVICE] No user roles found for user:", user.id);
        return [];
      }

      const isSystemAdmin = userRoles.some(role => role.role === "system_admin");
      console.log("[ORG_SERVICE] Is system admin:", isSystemAdmin);

  if (isSystemAdmin) {
    // System admin gets all organizations with hierarchy data
    const { data, error } = await supabase
      .from("organizations")
      .select("*")
      .order("hierarchy_level", { ascending: true })
      .order("name", { ascending: true });

    if (error) {
      console.error("[ORG_SERVICE] Error fetching organizations for system admin:", error);
      throw new Error(`Failed to fetch organizations: ${error.message}`);
    }

    const allOrgs = data || [];
    console.log("[ORG_SERVICE] System admin - fetched organizations:", allOrgs.length, allOrgs.map(org => org.name));

    // Add a virtual "All Organizations" entry for system admins - this should be the DEFAULT
    const allOrganizationsEntry: Organization = {
      id: "system-admin-all-orgs",
      name: "All Organizations",
      type: "system",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      owner_id: user.id,
      settings: {
        description: "System Administrator view of all organizations"
      },
      billing_info: null,
      subscription_tier: "enterprise",
      parent_id: null,
      hierarchy_level: -1,
      hierarchy_path: "system-admin-all-orgs"
    };

    const result = [allOrganizationsEntry, ...allOrgs];
    console.log("[ORG_SERVICE] System admin - returning organizations:", result.length, result.map(org => org.name));
    return result;
  }

  // Regular user gets their organizations with hierarchy data
  const orgIds = userRoles.map(role => role.organization_id).filter(Boolean) as string[];
  if (!orgIds.length) return [];

  const { data } = await supabase
    .from("organizations")
    .select("*")
    .in("id", orgIds)
    .order("hierarchy_level", { ascending: true })
    .order("name", { ascending: true });

      return data || [];
    }
  );
}

export async function getOrganizationHierarchy(user: User): Promise<Organization[]> {
  return requestDeduplicator.deduplicate(
    createCacheKey.organizationHierarchy(user.id),
    async () => {
      const userRoles = await getUserRoles(user.id);
      if (!userRoles?.length) return [];

      const isSystemAdmin = userRoles.some(role => role.role === "system_admin");

      if (isSystemAdmin) {
        // System admin gets all organizations with hierarchy
        const { data } = await supabase
          .from("organizations")
          .select("*")
          .order("hierarchy_level", { ascending: true })
          .order("name", { ascending: true });

        const allOrgs = data || [];

        // Add virtual "All Organizations" entry
        const allOrganizationsEntry: Organization = {
          id: "system-admin-all-orgs",
          name: "All Organizations",
          type: "system",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          owner_id: user.id,
          settings: { description: "System Administrator view of all organizations" },
          billing_info: null,
          subscription_tier: "enterprise",
          ...(allOrgs.length > 0 && allOrgs[0].parent_id !== undefined ? {
            parent_id: null,
            hierarchy_level: -1,
            hierarchy_path: "system-admin-all-orgs"
          } : {})
        } as Organization;

        return [allOrganizationsEntry, ...allOrgs];
      }

      // Regular user gets their organizations with hierarchy
      const orgIds = userRoles.map(role => role.organization_id).filter(Boolean) as string[];
      if (!orgIds.length) return [];

      const { data } = await supabase
        .from("organizations")
        .select("*")
        .in("id", orgIds)
        .order("hierarchy_level", { ascending: true })
        .order("name", { ascending: true });

      return data || [];
    },
    60 * 1000 // Cache for 1 minute
  );
}

// Unified user roles fetcher that gets all user role data at once
export async function getUserRoles(userId: string): Promise<Array<{role: string, organization_id: string | null}>> {
  return requestDeduplicator.deduplicate(
    createCacheKey.userRoles(userId),
    async () => {
      const { data, error } = await supabase
        .from("user_roles")
        .select("role, organization_id")
        .eq("user_id", userId);

      if (error) {
        throw new Error(`Failed to fetch user roles: ${error.message}`);
      }

      return data || [];
    },
    60 * 1000 // Cache for 1 minute since this rarely changes
  );
}

export async function isUserSystemAdmin(userId: string): Promise<boolean> {
  const roles = await getUserRoles(userId);
  return roles.some(role => role.role === "system_admin");
}

export async function hasUserOrganizations(userId: string): Promise<boolean> {
  const roles = await getUserRoles(userId);
  return roles.some(role => role.organization_id !== null);
}

export async function getOrganizationInvites(userEmail: string): Promise<Array<{ id: string }>> {
  return requestDeduplicator.deduplicate(
    createCacheKey.organizationInvites(userEmail),
    async () => {
      const { data, error } = await supabase
        .from("organization_invites")
        .select("id")
        .eq("email", userEmail)
        .eq("status", "pending")
        .limit(1);

      if (error) {
        console.error("Error checking invites:", error);
        return [];
      }

      return data || [];
    },
    30 * 1000 // Cache for 30 seconds
  );
}

export async function getDetailedOrganizationInvites(userEmail: string): Promise<Array<{
  id: string;
  role: string;
  expires_at: string;
  organization: {
    id: string;
    name: string;
    type: string;
  };
}>> {
  return requestDeduplicator.deduplicate(
    createCacheKey.organizationInvitesDetailed(userEmail),
    async () => {
      const { data, error } = await supabase
        .from("organization_invites")
        .select(`
          id,
          role,
          expires_at,
          organization:organizations (
            id,
            name,
            type
          )
        `)
        .eq("email", userEmail)
        .eq("status", "pending")
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      // Filter and transform the data to match the expected type
      const filteredData = (data || []).filter(item =>
        item.expires_at !== null && item.organization !== null
      ).map(item => ({
        id: item.id,
        role: item.role,
        expires_at: item.expires_at!,
        organization: item.organization!
      }));

      return filteredData;
    },
    30 * 1000 // Cache for 30 seconds
  );
}